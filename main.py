from maix import image, display, app, time, camera, touchscreen
import cv2
import numpy as np
import math
import gc
from micu_uart_lib import (
    SimpleUART, micu_printf, bind_variable,
    VariableContainer, clear_variable_bindings
)

# --------------------------- 配置参数区（所有过滤条件集中在这里） ---------------------------
# 矩形检测核心参数
MIN_CONTOUR_AREA = 500       # 最小轮廓面积（过滤小目标）
MAX_CONTOUR_AREA = 30000     # 最大轮廓面积（过滤大目标）
TARGET_SIDES = 4             # 目标边数（矩形为4）
BINARY_THRESHOLD = 66        # 二值化阈值

# 宽高比过滤参数（宽/高）
MIN_ASPECT_RATIO = 0.6       # 最小宽高比（高不超过宽的1.67倍）
MAX_ASPECT_RATIO = 1.7       # 最大宽高比（宽不超过高的1.7倍）

# 角度过滤参数（°）
MIN_ANGLE = 75               # 最小直角角度（接近90°）
MAX_ANGLE = 105               # 最大直角角度（接近90°）

# 对边长度一致性参数（比例）
MIN_OPPOSITE_RATIO = 0.7     # 最小对边比例（允许±20%偏差）
MAX_OPPOSITE_RATIO = 1.4     # 最大对边比例

# 透视变换与圆形参数
CORRECTED_WIDTH = 200        # 校正后矩形宽度
CORRECTED_HEIGHT = 150       # 校正后矩形高度
CIRCLE_RADIUS = 40           # 圆形轨迹半径
CIRCLE_NUM_POINTS = 24       # 圆形轨迹点数量

# 触摸按键参数
TOUCH_DEBOUNCE = 0.3         # 触摸防抖动时间（秒）

# 准心坐标（target坐标）
TARGET_X = 164              # 准心X坐标
TARGET_Y = 122                # 准心Y坐标

# 串口发送控制
SEND_ENABLED = True          # 数据包发送使能标志

# 圆形轨迹点切换参数
CIRCLE_SWITCH_THRESHOLD = 15  # 切换阈值（像素）- 提前切换
CIRCLE_SWITCH_DELAY = 0.2     # 切换延迟（秒）- 减少顿挫感
CIRCLE_PREDICTION_DISTANCE = 25  # 预测距离（像素）- 提前准备切换
SMOOTH_SWITCH_MODE = False     # 启用平滑切换模式

# 激光控制参数
LASER_CONTROL_ENABLED = True     # 激光控制功能使能
LASER_ERROR_TOLERANCE = 2        # 误差容差（像素）
LASER_CONFIRM_DELAY = 500        # 到达确认延时（毫秒）
LASER_ON_DURATION = 500          # 激光开启持续时间（毫秒）

# --------------------------- 紫色激光检测器类 ---------------------------
class PurpleLaserDetector:
    def __init__(self, pixel_radius=3):
        self.pixel_radius = pixel_radius
        self.kernel = np.ones((3, 3), np.uint8)

    def detect(self, img):
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        lower_purple = np.array([120, 90, 100])
        upper_purple = np.array([180, 255, 255])
        mask_purple = cv2.inRange(hsv, lower_purple, upper_purple)
        mask_purple = cv2.morphologyEx(mask_purple, cv2.MORPH_CLOSE, self.kernel)
        
        contours_purple, _ = cv2.findContours(mask_purple, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        laser_points = []
        
        for cnt in contours_purple:
            rect = cv2.minAreaRect(cnt)
            cx, cy = map(int, rect[0])
            laser_points.append((cx, cy))
            cv2.circle(img, (cx, cy), 3, (255, 0, 255), -1)
            cv2.putText(img, "Laser", (cx-20, cy-10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 0, 255), 1)
        
        return img, laser_points

# --------------------------- 虚拟按键类 ---------------------------
class VirtualButtons:
    def __init__(self):
        self.buttons = [
            [20, 200, 45, 20, "Center", "center"],    
            [80, 200, 50, 20, "Circle", "circle"],   
            [230, 200, 25, 20, "T-", "thresh_down"],
            [280, 200, 25, 20, "T+", "thresh_up"]
        ]


        self.last_touch_time = 0
        self.touch_debounce = TOUCH_DEBOUNCE  # 使用配置参数

    def check_touch(self, touch_x, touch_y):
        current_time = time.time()
        if current_time - self.last_touch_time < self.touch_debounce:
            return None

        # 坐标转换：触摸坐标(640×480) -> 显示坐标(320×240)
        display_x = touch_x // 2
        display_y = touch_y // 2

        print(f"原始触摸: ({touch_x},{touch_y}) -> 转换显示: ({display_x},{display_y})")

        # 使用按钮显示坐标进行检测
        for i, button in enumerate(self.buttons):
            area_x, area_y, area_w, area_h = button[0], button[1], button[2], button[3]
            button_name = button[4]

            if area_x <= display_x <= area_x + area_w and area_y <= display_y <= area_y + area_h:
                self.last_touch_time = current_time
                action = button[5]
                print(f"触摸命中: {button_name} -> {action}")
                return action

        print(f"触摸未命中任何按钮")
        return None

    def draw_buttons(self, img, current_mode, threshold=BINARY_THRESHOLD):
        for button in self.buttons:
            x, y, w, h, text, action = button
            if (action == "center" and current_mode == "center") or (action == "circle" and current_mode == "circle"):
                color = (0, 255, 255)
                thickness = 3
            elif action in ["thresh_up", "thresh_down"]:
                color = (0, 255, 0)
                thickness = 2
            else:
                color = (255, 255, 255)
                thickness = 2
            cv2.rectangle(img, (x, y), (x + w, y + h), color, thickness)
            text_x = x + (w - len(text) * 4) // 2
            text_y = y + (h + 6) // 2
            cv2.putText(img, text, (text_x, text_y), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
        cv2.putText(img, f"Thresh: {threshold}", (230, 190),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)

# 触摸屏初始化
def init_touchscreen():
    try:
        ts = touchscreen.TouchScreen()
        print("TouchScreen initialized successfully")
        return ts
    except Exception as e:
        print(f"TouchScreen init failed: {e}")
        return None

# --------------------------- 工具函数 ---------------------------
def generate_circle_points(center, radius, num_points):
    circle_points = []
    cx, cy = center
    for i in range(num_points):
        angle = 2 * math.pi * i / num_points
        x = int(cx + radius * math.cos(angle))
        y = int(cy + radius * math.sin(angle))
        circle_points.append((x, y))
    return circle_points

def update_circle_target_index(all_circle_points, current_index, target_x, target_y, current_time):
    """更新圆形轨迹点目标索引（平滑切换版本）

    Args:
        all_circle_points: 所有圆形轨迹点
        current_index: 当前目标点索引
        target_x, target_y: 准心坐标
        current_time: 当前时间

    Returns:
        tuple: (新索引, 是否切换, 到达目标时间)
    """
    global target_reached_time, last_switch_time

    if not all_circle_points or len(all_circle_points) == 0:
        return current_index, False, target_reached_time

    # 获取当前目标点
    if current_index >= len(all_circle_points):
        current_index = 0

    target_point_x, target_point_y = all_circle_points[current_index]

    # 计算与当前目标点的距离
    distance = math.sqrt((target_x - target_point_x)**2 + (target_y - target_point_y)**2)

    # 平滑切换逻辑
    if distance <= CIRCLE_SWITCH_THRESHOLD:
        if SMOOTH_SWITCH_MODE:
            # 立即切换，不等待延迟（最平滑）
            new_index = (current_index + 1) % len(all_circle_points)
            last_switch_time = current_time
            target_reached_time = 0
            print(f"平滑切换轨迹点: {current_index} -> {new_index} (距离: {distance:.1f})")
            return new_index, True, 0
        else:
            # 传统延迟切换模式
            if target_reached_time == 0:
                target_reached_time = current_time

            if current_time - target_reached_time >= CIRCLE_SWITCH_DELAY:
                new_index = (current_index + 1) % len(all_circle_points)
                last_switch_time = current_time
                target_reached_time = 0
                print(f"延迟切换轨迹点: {current_index} -> {new_index}")
                return new_index, True, 0

    # 预测性切换：当接近目标点时开始准备
    elif distance <= CIRCLE_PREDICTION_DISTANCE:
        # 可以在这里添加预测逻辑，比如调整目标点位置
        pass

    return current_index, False, target_reached_time

def send_target_center_diff_binary(target_x, target_y, center_x, center_y):
    """发送target坐标与圆心坐标差的二进制数据包

    数据包格式：0x78, 标识符, x高八位, x低八位, y高八位, y低八位, 0xFC

    标识符规则：
    - x为负，y为正：0xF0
    - x为正，y为负：0x0F
    - 都为负：0xFF
    - 都为正：0x00

    Args:
        target_x, target_y: 准心坐标
        center_x, center_y: 圆心坐标
    """
    # 检查发送使能标志
    if not SEND_ENABLED:
        return False

    try:
        # 计算坐标差值
        diff_x = center_x - target_x  # 正值：目标在右，负值：目标在左
        diff_y = center_y - target_y  # 正值：目标在下，负值：目标在上

        # 简单的标识符确定（基于正负号）
        if diff_x < 0 and diff_y >= 0:
            identifier = 0xF0  # x为负，y为正
        elif diff_x >= 0 and diff_y < 0:
            identifier = 0x0F  # x为正，y为负
        elif diff_x < 0 and diff_y < 0:
            identifier = 0xFF  # 都为负
        else:  # diff_x >= 0 and diff_y >= 0
            identifier = 0x00  # 都为正

        # 取绝对值并限制在16位范围内
        abs_x = min(abs(diff_x), 65535)
        abs_y = min(abs(diff_y), 65535)

        # 分解为高低八位
        x_high = (abs_x >> 8) & 0xFF
        x_low = abs_x & 0xFF
        y_high = (abs_y >> 8) & 0xFF
        y_low = abs_y & 0xFF

        # 构建数据包
        packet = bytes([0x78, identifier, x_high, x_low, y_high, y_low, 0xFC])

        # 打印坐标差和数据包
        id_desc = {0x00: "++", 0x0F: "+-", 0xF0: "-+", 0xFF: "--"}[identifier]
        print(f"坐标差: ({diff_x:+d},{diff_y:+d}) [{id_desc}] -> {' '.join([f'0x{b:02X}' for b in packet])}")

        # 发送二进制数据包
        if hasattr(uart, 'serial') and uart.serial:
            uart.serial.write(packet)
            return True
        else:
            return False

    except Exception as e:
        return False

def send_laser_control_packet(identifier, current_diff_x=0, current_diff_y=0):
    """发送激光控制数据包

    数据包格式：0x78, 标识符, x高八位, x低八位, y高八位, y低八位, 0xFC

    Args:
        identifier: 激光控制标识符(0x15开启, 0x25关闭)
        current_diff_x, current_diff_y: 当前误差值(可选)
    """
    # 检查发送使能标志
    if not SEND_ENABLED or not LASER_CONTROL_ENABLED:
        return False

    try:
        # 取绝对值并限制在16位范围内
        abs_x = min(abs(current_diff_x), 65535)
        abs_y = min(abs(current_diff_y), 65535)

        # 分解为高低八位
        x_high = (abs_x >> 8) & 0xFF
        x_low = abs_x & 0xFF
        y_high = (abs_y >> 8) & 0xFF
        y_low = abs_y & 0xFF

        # 构建数据包
        packet = bytes([0x78, identifier, x_high, x_low, y_high, y_low, 0xFC])

        # 打印激光控制数据包
        laser_action = "开启" if identifier == 0x15 else "关闭" if identifier == 0x25 else "未知"
        print(f"激光控制: {laser_action} [0x{identifier:02X}] -> {' '.join([f'0x{b:02X}' for b in packet])}")

        # 发送二进制数据包
        if hasattr(uart, 'serial') and uart.serial:
            uart.serial.write(packet)
            return True
        else:
            return False

    except Exception as e:
        return False

def process_uart_command():
    """处理串口接收到的hex指令

    指令说明：
    - 0xFF: 停止串口发送
    - 0x00: 开始发送指令
    """
    global SEND_ENABLED

    try:
        if uart and uart.is_initialized:
            # 使用receive方法获取数据
            received_data = uart.receive()
            if received_data:
                # 打印原始接收数据用于调试
                print(f"收到原始数据: {repr(received_data)}")
                print(f"数据类型: {type(received_data)}")

                # 如果收到的是乱码字符'�'，说明是0xFF被错误解码了
                if received_data == '�' or received_data == '\ufffd':
                    SEND_ENABLED = False
                    print("串口发送已停止 (收到0xFF，被解码为乱码字符)")
                    return

                # 处理字符串数据
                if isinstance(received_data, str):
                    # 检查每个字符的Unicode码点
                    for char in received_data:
                        unicode_val = ord(char)
                        print(f"字符: {repr(char)}, Unicode: U+{unicode_val:04X}")

                        # 检查是否是替换字符（表示原始0xFF）
                        if unicode_val == 0xFFFD:  # Unicode替换字符
                            SEND_ENABLED = False
                            print("串口发送已停止 (检测到Unicode替换字符，原始数据可能是0xFF)")
                        elif unicode_val == 0x00:
                            SEND_ENABLED = True
                            print("串口发送已启动 (收到0x00)")
                        # 如果字符在ASCII范围内，检查其值
                        elif unicode_val < 256:
                            if unicode_val == 0xFF:
                                SEND_ENABLED = False
                                print("串口发送已停止 (收到0xFF)")
                            elif unicode_val == 0x00:
                                SEND_ENABLED = True
                                print("串口发送已启动 (收到0x00)")

                # 处理字节数据
                elif isinstance(received_data, (bytes, bytearray)):
                    for byte_val in received_data:
                        print(f"处理字节: 0x{byte_val:02X}")
                        if byte_val == 0xFF:
                            SEND_ENABLED = False
                            print("串口发送已停止 (收到0xFF)")
                        elif byte_val == 0x00:
                            SEND_ENABLED = True
                            print("串口发送已启动 (收到0x00)")
    except Exception as e:
        print(f"串口指令处理异常: {e}")  # 显示异常信息用于调试

def perspective_transform(pts, target_width, target_height):
    s = pts.sum(axis=1)
    tl = pts[np.argmin(s)]
    br = pts[np.argmax(s)]
    diff = np.diff(pts, axis=1)
    tr = pts[np.argmin(diff)]
    bl = pts[np.argmax(diff)]
    src_pts = np.array([tl, tr, br, bl], dtype=np.float32)
    
    dst_pts = np.array([
        [0, 0], [target_width-1, 0],
        [target_width-1, target_height-1], [0, target_height-1]
    ], dtype=np.float32)
    
    M = cv2.getPerspectiveTransform(src_pts, dst_pts)
    ret, M_inv = cv2.invert(M)
    return M, M_inv, src_pts

def is_regular_rectangle(approx):
    """使用配置参数判断是否为规则矩形"""
    # 1. 凸性检查
    if not cv2.isContourConvex(approx):
        return False, "非凸多边形"
    
    # 2. 提取四个顶点
    pts = approx.reshape(4, 2).astype(np.float32)
    p0, p1, p2, p3 = pts[0], pts[1], pts[2], pts[3]
    
    # 3. 计算四条边的长度
    edge_lengths = [
        math.hypot(p1[0]-p0[0], p1[1]-p0[1]),  # 上边
        math.hypot(p2[0]-p1[0], p2[1]-p1[1]),  # 右边
        math.hypot(p3[0]-p2[0], p3[1]-p2[1]),  # 下边
        math.hypot(p0[0]-p3[0], p0[1]-p3[1])   # 左边
    ]
    top, right, bottom, left = edge_lengths
    
    # 4. 校验对边长度（使用配置参数）
    if not (MIN_OPPOSITE_RATIO <= top/bottom <= MAX_OPPOSITE_RATIO and 
            MIN_OPPOSITE_RATIO <= left/right <= MAX_OPPOSITE_RATIO):
        return False, f"对边不等（上/下={top/bottom:.2f}, 左/右={left/right:.2f}"
    
    # 5. 计算四个角的角度（使用配置参数）
    angles = []
    for i in range(4):
        p_prev = pts[i]
        p_curr = pts[(i+1)%4]
        p_next = pts[(i+2)%4]
        # 计算向量
        v1 = [p_curr[0]-p_prev[0], p_curr[1]-p_prev[1]]
        v2 = [p_next[0]-p_curr[0], p_next[1]-p_curr[1]]
        # 计算夹角（度）
        dot = v1[0]*v2[0] + v1[1]*v2[1]
        det = v1[0]*v2[1] - v1[1]*v2[0]
        angle = abs(math.degrees(math.atan2(det, dot)))
        angles.append(angle)
    
    if not all(MIN_ANGLE <= angle <= MAX_ANGLE for angle in angles):
        return False, f"角度异常 {[round(a,1) for a in angles]}"
    
    # 所有条件通过
    return True, "规则矩形"

# --------------------------- 主程序 ---------------------------
if __name__ == "__main__":
    gc.disable()
    print("融合版jiguangcar程序启动...")
    print("配置参数加载完成，可在代码开头调整过滤条件")
    
    # 初始化设备
    disp = display.Display()
    cam = camera.Camera(320, 240, image.Format.FMT_BGR888)
    laser_detector = PurpleLaserDetector()
    
    # 初始化虚拟按键和触摸屏
    buttons = VirtualButtons()
    ts = init_touchscreen()
    current_mode = "center"
    last_touch_pos = (0, 0)
    binary_threshold = BINARY_THRESHOLD  # 初始化阈值

    # 圆形轨迹点切换状态
    current_circle_index = 0      # 当前目标轨迹点索引
    last_switch_time = 0          # 上次切换时间
    target_reached_time = 0       # 到达目标的时间
    
    # 初始化串口
    uart = SimpleUART()
    if uart.init("/dev/ttyS0", 115200, set_as_global=True):
        print("串口初始化成功")
        # 禁用帧格式检测，直接处理原始数据
        uart.set_frame("$$", "##", False)
    else:
        print("串口初始化失败")
        exit()

    # FPS计算初始化
    fps = 0
    last_time = time.ticks_ms()
    frame_count = 0

    while not app.need_exit():
        frame_count += 1

        # 处理串口指令
        process_uart_command()

        # 处理触摸输入
        current_time = time.time()
        if ts and (current_time - buttons.last_touch_time) > buttons.touch_debounce:
            try:
                if ts.available():
                    touch_data = ts.read()
                    if len(touch_data) >= 3:
                        touch_x, touch_y, pressed = touch_data[0], touch_data[1], touch_data[2]
                        last_touch_pos = (touch_x, touch_y)
                        if pressed:
                            action = buttons.check_touch(touch_x, touch_y)
                            if action:
                                buttons.last_touch_time = current_time
                                if action == "center":
                                    current_mode = "center"
                                    print("切换到中心点模式")
                                elif action == "circle":
                                    current_mode = "circle"
                                    print("切换到圆形模式")
                                elif action == "thresh_up":
                                    binary_threshold = min(255, binary_threshold + 3)
                                    print(f"阈值增加到: {binary_threshold}")
                                elif action == "thresh_down":
                                    binary_threshold = max(1, binary_threshold - 3)
                                    print(f"阈值减少到: {binary_threshold}")
            except Exception as e:
                if frame_count % 120 == 0:
                    print(f"Touch processing error: {e}")
        
        # 计算FPS
        current_time_ms = time.ticks_ms()
        if current_time_ms - last_time > 0:
            fps = 1000.0 / (current_time_ms - last_time)
        last_time = current_time_ms
        
        # 读取图像
        img = cam.read()
        if img is None:
            continue
        img_cv = image.image2cv(img, ensure_bgr=False, copy=False)
        output = img_cv.copy()

        # 1. 矩形检测（使用配置参数过滤）
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        _, binary = cv2.threshold(gray, binary_threshold, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
        
        quads = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            # 1. 面积过滤（使用配置参数）
            if not (MIN_CONTOUR_AREA < area < MAX_CONTOUR_AREA):
                continue
            
            # 2. 多边形逼近与边数过滤（使用配置参数）
            epsilon = 0.03 * cv2.arcLength(cnt, True)
            approx = cv2.approxPolyDP(cnt, epsilon, True)
            if len(approx) != TARGET_SIDES:
                continue
            
            # 3. 宽高比过滤（使用配置参数）
            x, y, w, h = cv2.boundingRect(approx)
            if h == 0:
                continue
            aspect_ratio = w / h
            if not (MIN_ASPECT_RATIO <= aspect_ratio <= MAX_ASPECT_RATIO):
                print(f"过滤宽高比异常: {aspect_ratio:.2f} (配置范围 {MIN_ASPECT_RATIO}-{MAX_ASPECT_RATIO})")
                continue
            
            # 4. 规则性校验（使用配置参数）
            is_regular, reason = is_regular_rectangle(approx)
            if not is_regular:
                print(f"过滤畸形矩形: {reason} (角度范围 {MIN_ANGLE}-{MAX_ANGLE}°)")
                continue
            
            # 所有条件通过
            quads.append((approx, area))

        # 只保留面积最大的矩形
        inner_quads = []
        if quads:
            largest_quad = max(quads, key=lambda x: x[1])
            inner_quads = [largest_quad]

        # 2. 处理检测到的矩形
        center_points = []
        all_circle_points = []

        for approx, area in inner_quads:
            pts = approx.reshape(4, 2).astype(np.float32)
            cv2.drawContours(output, [approx], -1, (0, 255, 0), 2)

            # 透视变换获取中心点（使用配置参数）
            M, M_inv, src_pts = perspective_transform(pts, CORRECTED_WIDTH, CORRECTED_HEIGHT)
            if M_inv is not None:
                corrected_center = (CORRECTED_WIDTH//2, CORRECTED_HEIGHT//2)
                center_np = np.array([[corrected_center]], dtype=np.float32)
                original_center = cv2.perspectiveTransform(center_np, M_inv)[0][0]
                cx, cy = int(original_center[0]), int(original_center[1])
                cv2.circle(output, (cx, cy), 2, (255, 0, 0), -1)
                center_points.append((cx, cy))
            else:
                cx = int(np.mean(pts[:, 0]))
                cy = int(np.mean(pts[:, 1]))
                cv2.circle(output, (cx, cy), 5, (255, 0, 0), -1)
                center_points.append((cx, cy))

            # 圆形模式处理（使用配置参数）
            if current_mode == "circle":
                if M_inv is not None:
                    corrected_center = (CORRECTED_WIDTH//2, CORRECTED_HEIGHT//2)
                    corrected_circle = generate_circle_points(
                        corrected_center, CIRCLE_RADIUS, CIRCLE_NUM_POINTS
                    )
                    corrected_points_np = np.array([corrected_circle], dtype=np.float32)
                    original_points = cv2.perspectiveTransform(corrected_points_np, M_inv)[0]
                    original_points = [(int(x), int(y)) for x, y in original_points]
                    all_circle_points.extend(original_points)
                    for (x, y) in original_points:
                        cv2.circle(output, (x, y), 2, (0, 0, 255), -1)
                else:
                    simple_circle = generate_circle_points((cx, cy), 30, CIRCLE_NUM_POINTS)
                    all_circle_points.extend(simple_circle)
                    for (x, y) in simple_circle:
                        cv2.circle(output, (x, y), 2, (0, 0, 255), -1)

                # 在circle模式下，高亮显示当前目标轨迹点
                if current_mode == "circle" and all_circle_points and current_circle_index < len(all_circle_points):
                    target_x_circle, target_y_circle = all_circle_points[current_circle_index]
                    cv2.circle(output, (target_x_circle, target_y_circle), 5, (0, 255, 255), 2)  # 黄色圆圈
                    cv2.putText(output, f"T{current_circle_index}", (target_x_circle+8, target_y_circle-8),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 255, 255), 1)

        # 3. 激光检测（仅circle模式）
        if current_mode == "circle":
            output, laser_points = laser_detector.detect(output)

        # 4. 串口发送数据（只发送二进制数据包）
        if current_mode == "center":
            if center_points:
                cx, cy = center_points[0]
                # 只发送target与圆心坐标差的二进制数据包
                send_target_center_diff_binary(TARGET_X, TARGET_Y, cx, cy)
            # 识别不到矩形时不发送任何数据包
        elif current_mode == "circle":
            if all_circle_points:
                # 更新圆形轨迹点目标索引
                current_circle_index, switched, target_reached_time = update_circle_target_index(
                    all_circle_points, current_circle_index, TARGET_X, TARGET_Y, current_time
                )

                # 在圆形模式下，发送target与当前目标轨迹点的差值
                circle_x, circle_y = all_circle_points[current_circle_index]
                send_target_center_diff_binary(TARGET_X, TARGET_Y, circle_x, circle_y)
            # 识别不到矩形时不发送任何数据包

        # 5. 绘制目标点标记（准心小点）
        target_x, target_y = TARGET_X, TARGET_Y  # 使用配置参数
        cv2.circle(output, (target_x, target_y), 3, (255, 0, 255), -1)  # 实心小圆点

        # 6. 绘制虚拟按键
        buttons.draw_buttons(output, current_mode, binary_threshold)

        # 7. 显示统计信息
        cv2.putText(output, f"FPS: {fps:.1f}", (10, 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        mode_text = f"Mode: {current_mode.upper()}"
        cv2.putText(output, mode_text, (10, 40),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 255), 1)
        cv2.putText(output, f"Touch: {last_touch_pos}", (10, 55),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.3, (255, 255, 0), 1)

        # 显示target与目标点的坐标差（右上角）
        if current_mode == "center" and center_points:
            # 中心点模式：显示与矩形中心的差值
            cx, cy = center_points[0]
            diff_x = cx - TARGET_X
            diff_y = cy - TARGET_Y
            cv2.putText(output, f"Error: ({diff_x:+d},{diff_y:+d})", (200, 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 255), 1)
        elif current_mode == "circle" and all_circle_points:
            # 圆形模式：显示与当前目标轨迹点的差值
            circle_x, circle_y = all_circle_points[current_circle_index]
            diff_x = circle_x - TARGET_X
            diff_y = circle_y - TARGET_Y
            cv2.putText(output, f"Error: ({diff_x:+d},{diff_y:+d}) P{current_circle_index}", (200, 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 0, 255), 1)
        else:
            cv2.putText(output, f"Error: No Target", (200, 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (128, 128, 128), 1)

        # 显示发送状态
        send_status = "ON" if SEND_ENABLED else "OFF"
        send_color = (0, 255, 0) if SEND_ENABLED else (0, 0, 255)
        cv2.putText(output, f"Send: {send_status}", (10, 85),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.3, send_color, 1)

        # 显示图像
        img_show = image.cv2image(output, bgr=True, copy=False)
        disp.show(img_show)
